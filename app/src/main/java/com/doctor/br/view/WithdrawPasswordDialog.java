package com.doctor.br.view;

import android.app.Dialog;
import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.doctor.yy.R;
import org.newapp.ones.base.utils.LogUtils;

import java.lang.ref.WeakReference;

/**
 * 提现密码验证弹窗
 * 仿照 iOS 端 BRWithdrawPasswordView 实现
 */
public class WithdrawPasswordDialog extends Dialog implements View.OnClickListener {
    private static WeakReference<WithdrawPasswordDialog> sWeakReferenceInstance;
    private Context mContext;
    private TextView tvTitle;
    private EditText etPassword;
    private Button btnCancel;
    private Button btnConfirm;
    
    private OnPasswordVerificationListener onPasswordVerificationListener;
    
    public interface OnPasswordVerificationListener {
        void onPasswordConfirmed(String password);
        void onPasswordCanceled();
    }

    public static WithdrawPasswordDialog getInstance(Context context) {
        if (sWeakReferenceInstance != null) {
            sWeakReferenceInstance.clear();
            sWeakReferenceInstance = null;
        }
        sWeakReferenceInstance = new WeakReference<>(new WithdrawPasswordDialog(context, R.style.alert_dialog_style));
        return sWeakReferenceInstance.get();
    }

    private WithdrawPasswordDialog(Context context, int theme) {
        super(context, theme);
        initView(context);
    }

    /**
     * 初始化控件
     */
    private void initView(Context context) {
        this.mContext = context;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(R.layout.dialog_withdraw_password_verification, null);
        addContentView(layout, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        
        tvTitle = findViewById(R.id.tv_title);
        etPassword = findViewById(R.id.et_password);
        btnCancel = findViewById(R.id.btn_cancel);
        btnConfirm = findViewById(R.id.btn_confirm);
        
        // 设置密码输入框属性
        etPassword.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_VARIATION_PASSWORD);
        etPassword.setFilters(new InputFilter[]{new InputFilter.LengthFilter(6)});
        etPassword.setHint("请输入6位数字密码");
        
        // 添加文本监听器，确保只能输入数字
        etPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 只允许数字输入
                String input = s.toString();
                String numericOnly = input.replaceAll("[^0-9]", "");
                if (!input.equals(numericOnly)) {
                    etPassword.removeTextChangedListener(this);
                    etPassword.setText(numericOnly);
                    etPassword.setSelection(numericOnly.length());
                    etPassword.addTextChangedListener(this);
                }
                
                // 控制确认按钮状态
                updateConfirmButtonState();
            }
        });
        
        btnCancel.setOnClickListener(this);
        btnConfirm.setOnClickListener(this);
        
        // 初始状态确认按钮不可用
        updateConfirmButtonState();
    }
    
    /**
     * 更新确认按钮状态
     */
    private void updateConfirmButtonState() {
        String password = etPassword.getText().toString().trim();
        boolean isEnabled = password.length() == 6;
        btnConfirm.setEnabled(isEnabled);
        btnConfirm.setAlpha(isEnabled ? 1.0f : 0.5f);
    }

    /**
     * 设置标题
     */
    public WithdrawPasswordDialog setTitle(String title) {
        if (tvTitle != null) {
            tvTitle.setText(TextUtils.isEmpty(title) ? "请输入提现密码" : title);
        }
        return this;
    }

    /**
     * 设置密码验证监听器
     */
    public WithdrawPasswordDialog setOnPasswordVerificationListener(OnPasswordVerificationListener listener) {
        this.onPasswordVerificationListener = listener;
        return this;
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.btn_cancel) {
            if (onPasswordVerificationListener != null) {
                onPasswordVerificationListener.onPasswordCanceled();
            }
            dismiss();
        } else if (id == R.id.btn_confirm) {
            String password = etPassword.getText().toString().trim();
            if (password.length() == 6) {
                if (onPasswordVerificationListener != null) {
                    onPasswordVerificationListener.onPasswordConfirmed(password);
                }
                dismiss();
            }
        }
    }

    /**
     * 显示对话框
     */
    public void show() {
        try {
            super.show();
            // 清空密码输入框
            if (etPassword != null) {
                etPassword.setText("");
                etPassword.requestFocus();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 关闭对话框
     */
    public void dismiss() {
        try {
            if (etPassword != null) {
                etPassword.setText("");
            }
            super.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}